'use client';

import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import type { Product, Customer, Order } from '@/lib/odoo/schemas';

interface ApiResponse<T> {
  success: boolean;
  data: T[];
  count: number;
  limit: number;
  offset: number;
  error?: string;
  message?: string;
}

interface UseOdooState<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  count: number;
}

interface UseOdooOptions {
  limit?: number;
  offset?: number;
  search?: string;
  autoFetch?: boolean;
}

// Hook genérico para operações Odoo
function useOdooData<T>(
  endpoint: string,
  options: UseOdooOptions = {}
) {
  const { limit = 50, offset = 0, search, autoFetch = true } = options;
  
  const [state, setState] = useState<UseOdooState<T>>({
    data: [],
    loading: false,
    error: null,
    count: 0,
  });

  const fetchData = useCallback(async (params?: {
    limit?: number;
    offset?: number;
    search?: string;
  }) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const searchParams = new URLSearchParams();
      searchParams.set('limit', String(params?.limit || limit));
      searchParams.set('offset', String(params?.offset || offset));
      
      if (params?.search || search) {
        searchParams.set('search', params?.search || search || '');
      }

      const response = await axios.get<ApiResponse<T>>(
        `${endpoint}?${searchParams.toString()}`
      );

      if (response.data.success) {
        setState({
          data: response.data.data,
          loading: false,
          error: null,
          count: response.data.count,
        });
      } else {
        setState(prev => ({
          ...prev,
          loading: false,
          error: response.data.error || 'Erro desconhecido',
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: axios.isAxiosError(error) 
          ? error.response?.data?.message || error.message 
          : 'Erro ao carregar dados',
      }));
    }
  }, [endpoint, limit, offset, search]);

  const refresh = useCallback(() => {
    fetchData();
  }, [fetchData]);

  const searchData = useCallback((searchTerm: string) => {
    fetchData({ search: searchTerm, offset: 0 });
  }, [fetchData]);

  const loadMore = useCallback(() => {
    fetchData({ offset: state.data.length });
  }, [fetchData, state.data.length]);

  useEffect(() => {
    if (autoFetch) {
      fetchData();
    }
  }, [fetchData, autoFetch]);

  return {
    ...state,
    refresh,
    searchData,
    loadMore,
    fetchData,
  };
}

// Hook específico para produtos
export function useOdooProducts(options: UseOdooOptions = {}) {
  return useOdooData<Product>('/api/odoo/products', options);
}

// Hook específico para clientes
export function useOdooCustomers(options: UseOdooOptions = {}) {
  return useOdooData<Customer>('/api/odoo/customers', options);
}

// Hook específico para pedidos
export function useOdooOrders(options: UseOdooOptions = {}) {
  return useOdooData<Order>('/api/odoo/orders', options);
}

// Hook para teste de conexão
export function useOdooConnection() {
  const [state, setState] = useState<{
    connected: boolean | null;
    loading: boolean;
    error: string | null;
    uid?: number;
  }>({
    connected: null,
    loading: false,
    error: null,
  });

  const testConnection = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const response = await axios.get('/api/odoo/test');
      
      if (response.data.success) {
        setState({
          connected: true,
          loading: false,
          error: null,
          uid: response.data.uid,
        });
      } else {
        setState({
          connected: false,
          loading: false,
          error: response.data.message,
        });
      }
    } catch (error) {
      setState({
        connected: false,
        loading: false,
        error: axios.isAxiosError(error) 
          ? error.response?.data?.message || error.message 
          : 'Erro ao testar conexão',
      });
    }
  }, []);

  useEffect(() => {
    testConnection();
  }, [testConnection]);

  return {
    ...state,
    testConnection,
  };
}

// Hook para criar dados
export function useOdooCreate<T>(endpoint: string) {
  const [state, setState] = useState<{
    loading: boolean;
    error: string | null;
    success: boolean;
  }>({
    loading: false,
    error: null,
    success: false,
  });

  const create = useCallback(async (data: Partial<T>) => {
    setState({ loading: true, error: null, success: false });
    
    try {
      const response = await axios.post(endpoint, data);
      
      if (response.data.success) {
        setState({ loading: false, error: null, success: true });
        return response.data.id;
      } else {
        setState({ 
          loading: false, 
          error: response.data.error || 'Erro ao criar registro',
          success: false 
        });
        return null;
      }
    } catch (error) {
      setState({
        loading: false,
        error: axios.isAxiosError(error) 
          ? error.response?.data?.message || error.message 
          : 'Erro ao criar registro',
        success: false,
      });
      return null;
    }
  }, [endpoint]);

  const reset = useCallback(() => {
    setState({ loading: false, error: null, success: false });
  }, []);

  return {
    ...state,
    create,
    reset,
  };
}
