import axios, { AxiosInstance } from 'axios';
import type { 
  OdooConfig, 
  OdooAuthResponse, 
  OdooRPCRequest, 
  OdooRPCResponse 
} from './types';

export class OdooClient {
  private config: OdooConfig;
  private httpClient: AxiosInstance;
  private uid: number | null = null;
  private requestId = 1;

  constructor(config?: Partial<OdooConfig>) {
    this.config = {
      url: config?.url || process.env.ODOO_URL || '',
      database: config?.database || process.env.ODOO_DATABASE || '',
      username: config?.username || process.env.ODOO_USERNAME || '',
      apiKey: config?.apiKey || process.env.ODOO_API_KEY || '',
    };

    this.httpClient = axios.create({
      baseURL: this.config.url,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  private async makeRPCCall<T = any>(
    service: string, 
    method: string, 
    args: any[] = []
  ): Promise<T> {
    const request: OdooRPCRequest = {
      jsonrpc: '2.0',
      method: 'call',
      params: {
        service,
        method,
        args,
      },
      id: this.requestId++,
    };

    try {
      const response = await this.httpClient.post<OdooRPCResponse<T>>(
        '/jsonrpc',
        request
      );

      if (response.data.error) {
        throw new Error(
          `Odoo RPC Error: ${response.data.error.message} (Code: ${response.data.error.code})`
        );
      }

      return response.data.result as T;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(`HTTP Error: ${error.message}`);
      }
      throw error;
    }
  }

  async authenticate(): Promise<number> {
    if (this.uid) {
      return this.uid;
    }

    try {
      const result = await this.makeRPCCall<OdooAuthResponse>(
        'common',
        'authenticate',
        [this.config.database, this.config.username, this.config.apiKey, {}]
      );

      if (!result || typeof result !== 'number') {
        throw new Error('Authentication failed: Invalid credentials');
      }

      this.uid = result;
      return this.uid;
    } catch (error) {
      throw new Error(`Authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async searchRead(
    model: string,
    domain: any[] = [],
    fields: string[] = [],
    options: { limit?: number; offset?: number; order?: string } = {}
  ): Promise<any[]> {
    await this.authenticate();

    const args = [
      this.config.database,
      this.uid,
      this.config.apiKey,
      model,
      'search_read',
      [domain],
      {
        fields: fields.length > 0 ? fields : undefined,
        limit: options.limit,
        offset: options.offset,
        order: options.order,
      },
    ];

    return this.makeRPCCall('object', 'execute_kw', args);
  }

  async create(model: string, data: any): Promise<number> {
    await this.authenticate();

    const args = [
      this.config.database,
      this.uid,
      this.config.apiKey,
      model,
      'create',
      [data],
    ];

    return this.makeRPCCall('object', 'execute_kw', args);
  }

  async write(model: string, ids: number[], data: any): Promise<boolean> {
    await this.authenticate();

    const args = [
      this.config.database,
      this.uid,
      this.config.apiKey,
      model,
      'write',
      [ids, data],
    ];

    return this.makeRPCCall('object', 'execute_kw', args);
  }

  async unlink(model: string, ids: number[]): Promise<boolean> {
    await this.authenticate();

    const args = [
      this.config.database,
      this.uid,
      this.config.apiKey,
      model,
      'unlink',
      [ids],
    ];

    return this.makeRPCCall('object', 'execute_kw', args);
  }

  async search(
    model: string,
    domain: any[] = [],
    options: { limit?: number; offset?: number; order?: string } = {}
  ): Promise<number[]> {
    await this.authenticate();

    const args = [
      this.config.database,
      this.uid,
      this.config.apiKey,
      model,
      'search',
      [domain],
      options,
    ];

    return this.makeRPCCall('object', 'execute_kw', args);
  }

  async read(model: string, ids: number[], fields: string[] = []): Promise<any[]> {
    await this.authenticate();

    const args = [
      this.config.database,
      this.uid,
      this.config.apiKey,
      model,
      'read',
      [ids],
      { fields: fields.length > 0 ? fields : undefined },
    ];

    return this.makeRPCCall('object', 'execute_kw', args);
  }

  // Método para testar conectividade
  async testConnection(): Promise<{ success: boolean; message: string; uid?: number }> {
    try {
      const uid = await this.authenticate();
      return {
        success: true,
        message: 'Connection successful',
        uid,
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}
