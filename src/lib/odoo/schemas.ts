import { z } from 'zod';

// Schema para validação de produtos do Odoo
export const ProductSchema = z.object({
  id: z.number(),
  name: z.string(),
  list_price: z.number().min(0),
  description: z.string().optional().nullable(),
  categ_id: z.tuple([z.number(), z.string()]).optional().nullable(),
  qty_available: z.number().default(0),
  default_code: z.string().optional().nullable(),
  sale_ok: z.boolean().default(true),
  image_1920: z.string().optional().nullable(),
});

// Schema para validação de clientes do Odoo
export const CustomerSchema = z.object({
  id: z.number(),
  name: z.string().min(1, 'Nome é obrigatório'),
  email: z.string().email('Email inválido').optional().nullable(),
  phone: z.string().optional().nullable(),
  street: z.string().optional().nullable(),
  city: z.string().optional().nullable(),
  country_id: z.tuple([z.number(), z.string()]).optional().nullable(),
  is_company: z.boolean().default(false),
  customer_rank: z.number().default(1),
});

// Schema para linha de pedido
export const OrderLineSchema = z.object({
  id: z.number(),
  product_id: z.tuple([z.number(), z.string()]),
  product_uom_qty: z.number().min(0),
  price_unit: z.number().min(0),
  price_subtotal: z.number().min(0),
});

// Schema para validação de pedidos do Odoo
export const OrderSchema = z.object({
  id: z.number(),
  name: z.string(),
  partner_id: z.tuple([z.number(), z.string()]),
  order_line: z.array(OrderLineSchema).default([]),
  amount_total: z.number().min(0),
  state: z.string(),
  date_order: z.string(),
  currency_id: z.tuple([z.number(), z.string()]).optional().nullable(),
});

// Schemas para criação (sem ID obrigatório)
export const CreateProductSchema = ProductSchema.omit({ id: true });
export const CreateCustomerSchema = CustomerSchema.omit({ id: true });
export const CreateOrderSchema = OrderSchema.omit({ id: true });

// Schemas para atualização (campos opcionais)
export const UpdateProductSchema = ProductSchema.partial().omit({ id: true });
export const UpdateCustomerSchema = CustomerSchema.partial().omit({ id: true });
export const UpdateOrderSchema = OrderSchema.partial().omit({ id: true });

// Schema para resposta de autenticação
export const AuthResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  uid: z.number().optional(),
});

// Schema para configuração do Odoo
export const OdooConfigSchema = z.object({
  url: z.string().url('URL inválida'),
  database: z.string().min(1, 'Database é obrigatório'),
  username: z.string().min(1, 'Username é obrigatório'),
  apiKey: z.string().min(1, 'API Key é obrigatória'),
});

// Tipos TypeScript derivados dos schemas
export type Product = z.infer<typeof ProductSchema>;
export type Customer = z.infer<typeof CustomerSchema>;
export type Order = z.infer<typeof OrderSchema>;
export type OrderLine = z.infer<typeof OrderLineSchema>;
export type CreateProduct = z.infer<typeof CreateProductSchema>;
export type CreateCustomer = z.infer<typeof CreateCustomerSchema>;
export type CreateOrder = z.infer<typeof CreateOrderSchema>;
export type UpdateProduct = z.infer<typeof UpdateProductSchema>;
export type UpdateCustomer = z.infer<typeof UpdateCustomerSchema>;
export type UpdateOrder = z.infer<typeof UpdateOrderSchema>;
export type AuthResponse = z.infer<typeof AuthResponseSchema>;
export type OdooConfig = z.infer<typeof OdooConfigSchema>;

// Função utilitária para validar arrays de dados
export function validateProducts(data: unknown[]): Product[] {
  return data.map((item, index) => {
    try {
      return ProductSchema.parse(item);
    } catch (error) {
      console.error(`Erro ao validar produto no índice ${index}:`, error);
      throw new Error(`Produto inválido no índice ${index}`);
    }
  });
}

export function validateCustomers(data: unknown[]): Customer[] {
  return data.map((item, index) => {
    try {
      return CustomerSchema.parse(item);
    } catch (error) {
      console.error(`Erro ao validar cliente no índice ${index}:`, error);
      throw new Error(`Cliente inválido no índice ${index}`);
    }
  });
}

export function validateOrders(data: unknown[]): Order[] {
  return data.map((item, index) => {
    try {
      return OrderSchema.parse(item);
    } catch (error) {
      console.error(`Erro ao validar pedido no índice ${index}:`, error);
      throw new Error(`Pedido inválido no índice ${index}`);
    }
  });
}
