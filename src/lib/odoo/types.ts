// Tipos para integração com Odoo

export interface OdooConfig {
  url: string;
  database: string;
  username: string;
  apiKey: string;
}

export interface OdooAuthResponse {
  uid: number;
  session_id?: string;
}

export interface OdooProduct {
  id: number;
  name: string;
  list_price: number;
  description?: string;
  categ_id: [number, string];
  qty_available: number;
  default_code?: string;
  sale_ok: boolean;
  image_1920?: string;
}

export interface OdooCustomer {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  street?: string;
  city?: string;
  country_id?: [number, string];
  is_company: boolean;
  customer_rank: number;
}

export interface OdooOrder {
  id: number;
  name: string;
  partner_id: [number, string];
  order_line: OdooOrderLine[];
  amount_total: number;
  state: string;
  date_order: string;
  currency_id: [number, string];
}

export interface OdooOrderLine {
  id: number;
  product_id: [number, string];
  product_uom_qty: number;
  price_unit: number;
  price_subtotal: number;
}

export interface OdooRPCRequest {
  jsonrpc: string;
  method: string;
  params: any;
  id: number;
}

export interface OdooRPCResponse<T = any> {
  jsonrpc: string;
  id: number;
  result?: T;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}
