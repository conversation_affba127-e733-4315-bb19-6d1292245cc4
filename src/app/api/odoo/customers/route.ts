import { NextRequest, NextResponse } from 'next/server';
import { OdooClient } from '@/lib/odoo/client';
import { validateCustomers, CreateCustomerSchema } from '@/lib/odoo/schemas';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const search = searchParams.get('search');

    const odooClient = new OdooClient();
    
    // Construir domínio de busca - apenas clientes
    let domain: any[] = [['customer_rank', '>', 0]];
    
    if (search) {
      domain.push('|', ['name', 'ilike', search], ['email', 'ilike', search]);
    }

    const customers = await odooClient.searchRead(
      'res.partner',
      domain,
      [
        'name', 
        'email', 
        'phone', 
        'street', 
        'city', 
        'country_id',
        'is_company',
        'customer_rank'
      ],
      { limit, offset, order: 'name asc' }
    );
    
    // Validar dados com Zod
    const validatedCustomers = validateCustomers(customers);
    
    return NextResponse.json({
      success: true,
      data: validatedCustomers,
      count: validatedCustomers.length,
      limit,
      offset,
    });
  } catch (error) {
    console.error('Erro ao buscar clientes:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro ao buscar clientes',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validar dados de entrada
    const validatedData = CreateCustomerSchema.parse(body);
    
    const odooClient = new OdooClient();
    const customerId = await odooClient.create('res.partner', {
      ...validatedData,
      customer_rank: 1, // Marcar como cliente
    });
    
    return NextResponse.json({
      success: true,
      message: 'Cliente criado com sucesso',
      id: customerId,
    }, { status: 201 });
  } catch (error) {
    console.error('Erro ao criar cliente:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro ao criar cliente',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}
