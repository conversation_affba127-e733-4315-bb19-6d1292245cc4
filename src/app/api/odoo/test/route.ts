import { NextRequest, NextResponse } from 'next/server';
import { OdooClient } from '@/lib/odoo/client';

export async function GET(request: NextRequest) {
  try {
    const odooClient = new OdooClient();
    const result = await odooClient.testConnection();
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Conexão com Odoo estabelecida com sucesso',
        uid: result.uid,
        timestamp: new Date().toISOString(),
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: `Falha na conexão: ${result.message}`,
          timestamp: new Date().toISOString(),
        },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('Erro no teste de conexão Odoo:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Erro interno do servidor',
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
