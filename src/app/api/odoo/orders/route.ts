import { NextRequest, NextResponse } from 'next/server';
import { OdooClient } from '@/lib/odoo/client';
import { validateOrders, CreateOrderSchema } from '@/lib/odoo/schemas';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const customerId = searchParams.get('customer_id');
    const state = searchParams.get('state');

    const odooClient = new OdooClient();
    
    // Construir domínio de busca
    let domain: any[] = [];
    
    if (customerId) {
      domain.push(['partner_id', '=', parseInt(customerId)]);
    }
    
    if (state) {
      domain.push(['state', '=', state]);
    }

    const orders = await odooClient.searchRead(
      'sale.order',
      domain,
      [
        'name', 
        'partner_id', 
        'order_line', 
        'amount_total', 
        'state', 
        'date_order',
        'currency_id'
      ],
      { limit, offset, order: 'date_order desc' }
    );
    
    // Para cada pedido, buscar as linhas de pedido detalhadas
    const ordersWithLines = await Promise.all(
      orders.map(async (order) => {
        if (order.order_line && order.order_line.length > 0) {
          const orderLines = await odooClient.read(
            'sale.order.line',
            order.order_line,
            ['product_id', 'product_uom_qty', 'price_unit', 'price_subtotal']
          );
          order.order_line = orderLines;
        }
        return order;
      })
    );
    
    // Validar dados com Zod
    const validatedOrders = validateOrders(ordersWithLines);
    
    return NextResponse.json({
      success: true,
      data: validatedOrders,
      count: validatedOrders.length,
      limit,
      offset,
    });
  } catch (error) {
    console.error('Erro ao buscar pedidos:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro ao buscar pedidos',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validar dados de entrada
    const validatedData = CreateOrderSchema.parse(body);
    
    const odooClient = new OdooClient();
    const orderId = await odooClient.create('sale.order', validatedData);
    
    return NextResponse.json({
      success: true,
      message: 'Pedido criado com sucesso',
      id: orderId,
    }, { status: 201 });
  } catch (error) {
    console.error('Erro ao criar pedido:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro ao criar pedido',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}
