import { NextRequest, NextResponse } from 'next/server';
import { OdooClient } from '@/lib/odoo/client';
import { validateProducts, CreateProductSchema } from '@/lib/odoo/schemas';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const search = searchParams.get('search');

    const odooClient = new OdooClient();
    
    // Construir domínio de busca
    let domain: any[] = [['sale_ok', '=', true]];
    
    if (search) {
      domain.push(['name', 'ilike', search]);
    }

    const products = await odooClient.searchRead(
      'product.product',
      domain,
      [
        'name', 
        'list_price', 
        'description', 
        'categ_id', 
        'qty_available',
        'default_code',
        'sale_ok',
        'image_1920'
      ],
      { limit, offset, order: 'name asc' }
    );
    
    // Validar dados com Zod
    const validatedProducts = validateProducts(products);
    
    return NextResponse.json({
      success: true,
      data: validatedProducts,
      count: validatedProducts.length,
      limit,
      offset,
    });
  } catch (error) {
    console.error('Erro ao buscar produtos:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro ao buscar produtos',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validar dados de entrada
    const validatedData = CreateProductSchema.parse(body);
    
    const odooClient = new OdooClient();
    const productId = await odooClient.create('product.product', validatedData);
    
    return NextResponse.json({
      success: true,
      message: 'Produto criado com sucesso',
      id: productId,
    }, { status: 201 });
  } catch (error) {
    console.error('Erro ao criar produto:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro ao criar produto',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}
