'use client';

import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useOdooConnection } from '@/hooks/useOdoo';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import {
  Package,
  Users,
  ShoppingCart,
  CheckCircle,
  XCircle,
  Wifi,
  WifiOff
} from 'lucide-react';

export default function HomePage() {
  const { connected, loading, error, testConnection, uid } = useOdooConnection();

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4 text-primary">Ryrox Rental</h1>
        <p className="text-xl text-muted-foreground mb-8">
          Sistema de Aluguel de Equipamentos com Integração Odoo
        </p>

        {/* Status de Conexão */}
        <Card className="max-w-md mx-auto mb-8">
          <CardContent className="pt-6">
            {loading ? (
              <LoadingSpinner text="Testando conexão..." />
            ) : connected === true ? (
              <div className="flex items-center justify-center gap-2 text-green-600">
                <CheckCircle className="h-5 w-5" />
                <Wifi className="h-5 w-5" />
                <span className="font-medium">Conectado ao Odoo</span>
                {uid && <span className="text-sm text-muted-foreground">(UID: {uid})</span>}
              </div>
            ) : connected === false ? (
              <div className="space-y-3">
                <div className="flex items-center justify-center gap-2 text-red-600">
                  <XCircle className="h-5 w-5" />
                  <WifiOff className="h-5 w-5" />
                  <span className="font-medium">Erro de Conexão</span>
                </div>
                {error && (
                  <p className="text-sm text-muted-foreground">{error}</p>
                )}
                <Button onClick={testConnection} size="sm" variant="outline">
                  Tentar Novamente
                </Button>
              </div>
            ) : null}
          </CardContent>
        </Card>

        {/* Navegação Principal */}
        <div className="flex flex-wrap gap-4 justify-center">
          <Button asChild size="lg">
            <Link href="/products">
              <Package className="h-5 w-5 mr-2" />
              Ver Produtos
            </Link>
          </Button>
          <Button asChild variant="outline" size="lg">
            <Link href="/customers">
              <Users className="h-5 w-5 mr-2" />
              Clientes
            </Link>
          </Button>
          <Button asChild variant="outline" size="lg">
            <Link href="/orders">
              <ShoppingCart className="h-5 w-5 mr-2" />
              Pedidos
            </Link>
          </Button>
        </div>
      </div>

      {/* Cards de Funcionalidades */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
        <Card className="text-center hover:shadow-lg transition-shadow">
          <CardHeader>
            <Package className="h-12 w-12 mx-auto text-primary mb-2" />
            <CardTitle>Catálogo de Produtos</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Visualize todos os produtos disponíveis para locação,
              sincronizados diretamente com o Odoo.
            </p>
          </CardContent>
        </Card>

        <Card className="text-center hover:shadow-lg transition-shadow">
          <CardHeader>
            <Users className="h-12 w-12 mx-auto text-primary mb-2" />
            <CardTitle>Gestão de Clientes</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Gerencie informações de clientes com integração
              completa ao sistema Odoo.
            </p>
          </CardContent>
        </Card>

        <Card className="text-center hover:shadow-lg transition-shadow">
          <CardHeader>
            <ShoppingCart className="h-12 w-12 mx-auto text-primary mb-2" />
            <CardTitle>Controle de Pedidos</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Acompanhe todos os pedidos e locações em tempo real
              com dados atualizados do Odoo.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Informações Técnicas */}
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="text-center">Sobre o Sistema</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold mb-2">Tecnologias Utilizadas</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Next.js 15 com React 19</li>
                <li>• TypeScript para tipagem</li>
                <li>• Tailwind CSS para estilização</li>
                <li>• Zod para validação de dados</li>
                <li>• Axios para requisições HTTP</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Integração Odoo</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• API JSON-RPC do Odoo</li>
                <li>• Autenticação segura</li>
                <li>• Validação de dados em tempo real</li>
                <li>• Sincronização automática</li>
                <li>• Tratamento robusto de erros</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
