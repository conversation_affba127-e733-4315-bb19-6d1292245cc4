'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useOdooCreate } from '@/hooks/useOdoo';
import type { CreateCustomer } from '@/lib/odoo/schemas';
import { User, Mail, Phone, MapPin, Building } from 'lucide-react';

interface CustomerFormProps {
  onSuccess?: (customerId: number) => void;
  onCancel?: () => void;
}

export function CustomerForm({ onSuccess, onCancel }: CustomerFormProps) {
  const [formData, setFormData] = useState<CreateCustomer>({
    name: '',
    email: '',
    phone: '',
    street: '',
    city: '',
    is_company: false,
    customer_rank: 1,
  });

  const { create, loading, error, success, reset } = useOdooCreate<CreateCustomer>('/api/odoo/customers');

  const handleInputChange = (field: keyof CreateCustomer, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const customerId = await create(formData);
    
    if (customerId && onSuccess) {
      onSuccess(customerId);
      // Reset form
      setFormData({
        name: '',
        email: '',
        phone: '',
        street: '',
        city: '',
        is_company: false,
        customer_rank: 1,
      });
    }
  };

  const handleCancel = () => {
    reset();
    setFormData({
      name: '',
      email: '',
      phone: '',
      street: '',
      city: '',
      is_company: false,
      customer_rank: 1,
    });
    if (onCancel) {
      onCancel();
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Novo Cliente
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nome *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Nome completo"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="email"
                  type="email"
                  value={formData.email || ''}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="phone">Telefone</Label>
              <div className="relative">
                <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="phone"
                  value={formData.phone || ''}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="(11) 99999-9999"
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="city">Cidade</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="city"
                  value={formData.city || ''}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  placeholder="Cidade"
                  className="pl-10"
                />
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="street">Endereço</Label>
            <Input
              id="street"
              value={formData.street || ''}
              onChange={(e) => handleInputChange('street', e.target.value)}
              placeholder="Rua, número, bairro"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="is_company"
              checked={formData.is_company}
              onChange={(e) => handleInputChange('is_company', e.target.checked)}
              className="rounded border-gray-300"
            />
            <Label htmlFor="is_company" className="flex items-center gap-2">
              <Building className="h-4 w-4" />
              É uma empresa
            </Label>
          </div>
          
          {error && (
            <div className="p-3 text-sm text-red-600 bg-red-50 rounded-md">
              {error}
            </div>
          )}
          
          {success && (
            <div className="p-3 text-sm text-green-600 bg-green-50 rounded-md">
              Cliente criado com sucesso!
            </div>
          )}
          
          <div className="flex gap-2 pt-4">
            <Button type="submit" disabled={loading || !formData.name}>
              {loading ? 'Criando...' : 'Criar Cliente'}
            </Button>
            <Button type="button" variant="outline" onClick={handleCancel}>
              Cancelar
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
