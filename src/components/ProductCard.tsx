'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import type { Product } from '@/lib/odoo/schemas';
import { ShoppingCart, Package } from 'lucide-react';

interface ProductCardProps {
  product: Product;
  onAddToCart?: (product: Product) => void;
}

export function ProductCard({ product, onAddToCart }: ProductCardProps) {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(price);
  };

  const handleAddToCart = () => {
    if (onAddToCart) {
      onAddToCart(product);
    }
  };

  return (
    <Card className="w-full max-w-sm hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg line-clamp-2">{product.name}</CardTitle>
          {product.default_code && (
            <Badge variant="secondary" className="ml-2 text-xs">
              {product.default_code}
            </Badge>
          )}
        </div>
        {product.categ_id && (
          <p className="text-sm text-muted-foreground">
            {product.categ_id[1]}
          </p>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        {product.description && (
          <p className="text-sm text-gray-600 line-clamp-3">
            {product.description}
          </p>
        )}
        
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-2xl font-bold text-primary">
              {formatPrice(product.list_price)}
            </p>
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Package className="h-4 w-4" />
              <span>Estoque: {product.qty_available}</span>
            </div>
          </div>
          
          <div className="flex flex-col gap-2">
            <Button
              onClick={handleAddToCart}
              disabled={product.qty_available <= 0}
              className="w-full"
              size="sm"
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              {product.qty_available > 0 ? 'Adicionar' : 'Sem Estoque'}
            </Button>
          </div>
        </div>
        
        {!product.sale_ok && (
          <Badge variant="destructive" className="w-full justify-center">
            Não disponível para venda
          </Badge>
        )}
      </CardContent>
    </Card>
  );
}
