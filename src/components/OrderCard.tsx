'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { Order } from '@/lib/odoo/schemas';
import { Calendar, User, DollarSign, Package } from 'lucide-react';

interface OrderCardProps {
  order: Order;
  onClick?: (order: Order) => void;
}

export function OrderCard({ order, onClick }: OrderCardProps) {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getStateColor = (state: string) => {
    switch (state) {
      case 'draft':
        return 'secondary';
      case 'sent':
        return 'outline';
      case 'sale':
        return 'default';
      case 'done':
        return 'default';
      case 'cancel':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getStateLabel = (state: string) => {
    switch (state) {
      case 'draft':
        return 'Rascunho';
      case 'sent':
        return 'Enviado';
      case 'sale':
        return 'Confirmado';
      case 'done':
        return 'Concluído';
      case 'cancel':
        return 'Cancelado';
      default:
        return state;
    }
  };

  const handleClick = () => {
    if (onClick) {
      onClick(order);
    }
  };

  return (
    <Card 
      className={`w-full hover:shadow-lg transition-shadow ${onClick ? 'cursor-pointer' : ''}`}
      onClick={handleClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg">{order.name}</CardTitle>
          <Badge variant={getStateColor(order.state)}>
            {getStateLabel(order.state)}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <User className="h-4 w-4" />
          <span>{order.partner_id[1]}</span>
        </div>
        
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Calendar className="h-4 w-4" />
          <span>{formatDate(order.date_order)}</span>
        </div>
        
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Package className="h-4 w-4" />
          <span>{order.order_line.length} item(s)</span>
        </div>
        
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-primary" />
            <span className="text-sm text-muted-foreground">Total:</span>
          </div>
          <span className="text-lg font-bold text-primary">
            {formatPrice(order.amount_total)}
          </span>
        </div>
        
        {order.currency_id && (
          <div className="text-xs text-muted-foreground">
            Moeda: {order.currency_id[1]}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
